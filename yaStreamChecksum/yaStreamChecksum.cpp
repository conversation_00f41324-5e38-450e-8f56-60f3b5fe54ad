#include <yaEngineNext/nxt_engine.h>
#include <pcap/pcap.h>
#include <glib-2.0/glib.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/filesystem.hpp>
#include <string.h>
#include <libgen.h> // for api basename

struct sessionContext
{
public:
    char sessionName[60];
    GChecksum *checksum = NULL;

public:
    sessionContext():sessionName{}, checksum(NULL)
    {
    }
};

int show_field_kv(pfield_t *field, const char *kvFormat)
{
    pfield_desc_t *desc  = precord_field_get_fdesc(field);
    ya_fvalue_t   *value = precord_field_get_fvalue(field);
    if (NULL == value)
    {
        return 0;
    }

    char *value_string = ya_fvalue_to_string_repr(value, BASE_NONE);
    printf(kvFormat, pfdesc_get_name(desc), value_string);
    ya_fvalue_free_string_repr(value_string);

    return 0;
}

int showProtoRecord(precord_t *precord)
{
    player_t *layer = precord_layer_get_top(precord);
    const char *proto_name = precord_layer_get_layer_name(layer);

    printf("proto %s {", proto_name);

    // 打印第一个非空 field
    pfield_t *field = NULL;
    for (field = precord_field_get_first_from_layer_of(layer);
         field != NULL && precord_field_get_fvalue(field) == NULL;
         field = precord_field_get_next(precord, field))
        ; // blank
    show_field_kv(field, "%s: %s");

    // 打印剩余 field
    for (field = precord_field_get_next(precord, field);
         field != NULL;
         field = precord_field_get_next(precord, field))
    {
        show_field_kv(field, ", %s: %s");
    }

    printf("}\n");

    return 0;
}

int calcStoryChecksum(precord_t *precord, void *userdata)
{
    player_t *layer = precord_layer_get_by_name(precord, "story");
    if (NULL == layer)
    {
        return -1;
    }

    ya_fvalue_t* value_story = precord_fvalue_get_from_layer_of(layer, "story");
    if (NULL == value_story)
    {
        return -1;
    }

    GChecksum* checksum = (GChecksum *)(userdata);
    g_checksum_update(checksum, (guchar *)ya_fvalue_get(value_story), ya_fvalue_length(value_story));
    return 0;
}

typedef int (*cb_onPacket_func)(const uint8_t *pktData, uint32_t pktLen, uint64_t timestamp, void *userdata);

int pcap_packet_foreach(const char *filepath, cb_onPacket_func onPacket, void *userdata)
{
    // open pcap
    char errbuf[PCAP_ERRBUF_SIZE] = { 0 };
    pcap_t *pcap_handle = pcap_open_offline(filepath, errbuf);
    if (NULL == pcap_handle)
    {
        printf("pcap_open_offline error:%s\n", errbuf);
        return -1;
    }

    pcap_pkthdr   *pkt_header = NULL;
    const uint8_t *pkt_data   = NULL;
    int            lSts       = 0;

    for (; ; )
    {
        lSts = pcap_next_ex(pcap_handle, &pkt_header, &pkt_data);
        if (0 == lSts || 1 == lSts)
        {  // OK, copy it
            onPacket(pkt_data, pkt_header->len, pkt_header->ts.tv_sec, userdata);
        }
        else if (-1 == lSts)
        {   // error
            fprintf(stderr, "%s\n", pcap_geterr(pcap_handle));
            break;
        }
        else if (-2 == lSts)
        {   // no more pkt to read from offline file
            break;
        }
    }

    pcap_close(pcap_handle);
    return 0;
}

int event_handler_session_message(nxt_engine_t *engine, nxt_pmessage_t *message, void *userdata)
{
    auto           sessionCtx = (struct sessionContext *)(userdata);
    precord_t*     precord    = nxt_message_get_precord(message);
    nxt_session_t *session    = nxt_message_get_session(message);

    if (strcmp(sessionCtx->sessionName, "") == 0 && session)
    {
        const char *session_repr = nxt_session_get_str_repr(engine, session);
        strncpy(sessionCtx->sessionName, session_repr, sizeof sessionCtx->sessionName);
    }

    calcStoryChecksum(precord, sessionCtx->checksum);

    return 0;
}

int nxt_cb_onPacket(const uint8_t *pktData, uint32_t pktLen, uint64_t timestamp _U_, void *userdata)
{
    nxt_engine_t *engine = (nxt_engine_t *)(userdata);
    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), pktData, pktLen);

    // 输入 mbuf 到 engine 中，推动 engine 运转;
    // TODO: 需要将 init 与 free 放到 nxt_engine_run 中进行;
    nxt_engine_run(engine, mbuf);
    nxt_mbuf_free_wa(nxt_engine_get_allocator(engine), mbuf);

    return 0;
}

int load_plugins()
{
    // 加载插件
    char error_buff[255] = { 0 };
    std::string plugin_dir(yv::getAppDir() + "plugins");

    const char plugin_prefix[] = "yaNxtDissector";

    yv::forDirEntry(plugin_dir.c_str(), [&](const char *file_path, bool)
    {
        const char *file_name = basename(const_cast<char*>(file_path));
        if (strncmp(file_name, plugin_prefix, sizeof(plugin_prefix) - 1) != 0)
        {   // not a yaNxtDissector
            return -1;
        }

        nxt_plugin_dissector_load(file_path, error_buff, sizeof error_buff);
        printf("load plugin done: %s\n", file_path);
        return 0;
    });

    return 0;
}

int main(int, char *argv[])
{
    nxt_init();

    load_plugins();

    // 创建 engine
    nxt_engine_t *engine  = nxt_engine_create(nullptr);

    sessionContext sessionCtx;
    sessionCtx.checksum = g_checksum_new(G_CHECKSUM_MD5);

    // 添加事件处理器
    nxt_engine_add_event_handler(engine, NXT_EVENT_SESSION_MESSAGE, event_handler_session_message, &sessionCtx);

    // 解析 pcap 中的 packet
    pcap_packet_foreach(argv[1], nxt_cb_onPacket, engine);

    // 销毁 engine
    nxt_engine_destroy(engine);

    nxt_fini();

    // show checksum
    printf("stream [%s] md5:%s\n", sessionCtx.sessionName, g_checksum_get_string(sessionCtx.checksum));
    g_checksum_free(sessionCtx.checksum);
    return 0;
}
