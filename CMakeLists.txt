#cmake version
cmake_minimum_required(VERSION 3.14)
project(testyaEngineNext LANGUAGES C CXX VERSION 1.0.24)

#
# enable testing
#
# 配置 memcheck 不报告 'still reachable', 默认会误报 glib 存在泄漏
set(MEMORYCHECK_COMMAND_OPTIONS "-q --tool=memcheck --leak-check=full --num-callers=50 --trace-children=yes")
include(CTest)
enable_testing()

# generate compile_commands.json
set(CMAKE_EXPORT_COMPILE_COMMANDS on)

# variables
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)

#
# yaStreamChecksum
#
add_subdirectory(yaStreamChecksum)
add_subdirectory(yaLiteDpi)

#
# test case
#
add_test(NAME test_story_checksum
  COMMAND $<TARGET_FILE:yaStreamChecksum> ${PROJECT_SOURCE_DIR}/pcaps/proto-story-alice.pcap
)

# test_story_checksum 测试用例的输出必须满足以下匹配
set_tests_properties(test_story_checksum PROPERTIES
  PASS_REGULAR_EXPRESSION "4ee77d1236c6b8b6e23f26312a78487c" # 输出内容中必须含有该 md5 串
)
