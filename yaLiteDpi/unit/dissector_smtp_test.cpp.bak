#include "session.h"
#include "engine.h"
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_ringbuf.h>
#include <yaEngineNext/nxt_util.h>

#include <vector>
#include <string>

#include <gmock/gmock.h>

using ::testing::_;

// EQ_OP 取值有:
// EQ, NE, LE, LT, GE, GT
// STREQ, STRNE, STRCASEEQ, STRCASENE
// FLOAT_EQ, DOUBLE_EQ
#define EXPECT_PRECORD_MATCH(EQ_OP, precord, field_name, TYPE, expect_value) \
    do {                                                                     \
    pfield_t *field      = precord_field_get_by_name(precord, field_name);   \
    ASSERT_TRUE(field   != NULL);                                            \
    ya_fvalue_t *fvalue  = precord_field_get_fvalue(field);                  \
    ASSERT_TRUE(fvalue  != NULL);                                            \
    auto actual_value    = ya_fvalue_get_##TYPE(fvalue);                     \
    EXPECT_##EQ_OP(actual_value, expect_value);                              \
    } while (0)

class MockSession : public nxt_Session
{
public:
    using nxt_Session::nxt_Session;

public:
    MOCK_METHOD(int, readToRingbuf, (nxt_direction_enum direction, nxt_ringbuf_t *rbuf,
                                     uint32_t read_len, nxt_stream_read_res_t *read_status), (override));

    MOCK_METHOD(int, onEvent, (nxt_event event, nxt_mbuf_t *mbuf, precord_t *precord), (override));
};

class http_dissector : public testing::Test
{
public:
    static const bool kShowMsg    = true;
    static const bool kNotShowMsg = false;

public:
    void SetUp() override
    {
        engine_ = nxt_engine_create(NULL);

        // 创建 session, 需要 t5;
        nxt_tuple_5_ipv4_t t5{0, 0, 0, 0, 0};
        session_ = new MockSession(engine_->getIpv4TcpSessionKeeper(), 0, &t5);

        // 模拟 session 被识别为 http, 让 session userdata 被创建;
        auto httpDissector = nxt_dissector_get_by_name("http");
        nxt_session_on_proto_recognized(engine_, session_, httpDissector);
    }

    void TearDown() override
    {
        clearMsgs();

        delete session_;
        nxt_engine_destroy(engine_);
    }

    void clearMsgs()
    {
        for (auto& precord : msgArray_)
        {
            precord_destroy(precord);
        }

        msgArray_.clear();
    }

    void testDissect(const std::vector<std::string> &httpStream, nxt_direction_enum dir = NXT_DIR_C2S, bool showMsg = kNotShowMsg)
    {
        // 处理的消息为 C2S 方向;
        nxt_engine_regzone_put_direction(engine_, dir);

        // mock session->readToRingbuf 方法，将 http_stream 中的数据
        // 喂给 ringbuf, 让 dissector 流程顺畅;
        auto iter = httpStream.begin();
        EXPECT_CALL(*session_, readToRingbuf(_, _, _, _))
            .WillRepeatedly([&](nxt_direction_enum direction _U_, nxt_ringbuf_t *rbuf,
                                uint32_t readLen _U_, nxt_stream_read_res_t *readStatus _U_)
            {
                if (iter == httpStream.end())
                {
                    return 0;
                }

                auto segment = *iter++;
                std::cout << "repeat ----------------" << std::endl;
                std::cout << segment << std::endl;
                nxt_ringbuf_push_back(rbuf, (const uint8_t*)segment.data(), segment.length());
                return (int)segment.length();
            });

        // mock session->onEvent 方法，将解析过程中产生的 precord 保存到 outputPrecords 中;
        EXPECT_CALL(*session_, onEvent(_, _, _))
            .WillRepeatedly([&](nxt_event event, nxt_mbuf_t *mbuf _U_, precord_t *precord)
            {
                if (NXT_EVENT_SESSION_MESSAGE != event)
                {
                    return 0;
                }

                auto precordCopy = precord_clone(precord);
                msgArray_.push_back(precordCopy);
                return 0;
            });

        // 清空消息，防止上次解析结果残留;
        clearMsgs();

        // 执行被测试的函数: http_dissect
        nxt_dissector_t *httpDissector = nxt_dissector_get_by_name("http");
        nxt_dissector_do_dissect(engine_, httpDissector, session_, NULL);

        // 打印结果
        if (showMsg)
        {
            for (auto &precord : msgArray_)
            {
                nxt_util_show_precord(precord);
            }
        }
    }

protected:
    nxt_engine_t *engine_  = NULL;
    MockSession  *session_ = NULL;
    std::vector<precord_t *> msgArray_;
};


TEST_F(http_dissector, 2_msg_pipeline)
{
    std::vector<std::string> httpStream = {
        {"GET /seupdater.gif?res=0 HTTP/1.1\r\n"
         "Host: p3p.sogou.com\r\n"
         "Cache-Control: no-cache\r\n"
         "User-Agent: MicroMessenger Client\r\n"
         "Cookie: IMEVER=9.8.0.3746\r\n"
         "\r\n"},

        {"GET /hello HTTP/1.1\r\n"
         "Host: p3p.sogou.com\r\n"
         "Cache-Control: no-cache\r\n"
         "User-Agent: firefox\r\n"
         "Cookie: name=John\r\n"
         "\r\n"},
        {"GET /hello HTTP/1.1\r\n"
         "Host: p3p.sogou.com\r\n"
         "Cache-Control: no-cache\r\n"
         "User-Agent: firefox\r\n"
         "Cookie: name=John\r\n"
         "\r\n"},
        {"GET /hello HTTP/1.1\r\n"
         "Host: p3p.sogou.com\r\n"
         "Cache-Control: no-cache\r\n"
         "User-Agent: firefox\r\n"
         "Cookie: name=John\r\n"
         "\r\n"},
         {"hello world"},
         {"hello worlddd"},
        // {"220 WIN-9ETNELBLVG7 ESMTP\r\n"},
        // {"EHLO WINDOWS7-10"},
        // {"250-WIN-9ETNELBLVG7"},
        // {"250-SIZE 102400000"},
        // {"250-AUTH LOGIN"},
        // {"250 HELP"},
        // {"AUTH LOGIN"},
        // {"334 VXNlcm5hbWU6"},
        // {"cmVjdjJAdGVzdC5vcmc="},
        // {"334 UGFzc3dvcmQ6"},
        // {"cmVjdjI="},
        // {"235 authenticated."},
        // {"MAIL FROM: <<EMAIL>> SIZE=1524572"},
        // {"250 OK"},
        // {"RCPT TO: <<EMAIL>>"},
        // {"250 OK"},
        // {"DATA"},
        // {"354 OK, send."},
        // {
        //  "Date: Wed, 7 Dec 2022 16:59:10 +0800"
        //  "From: \"<EMAIL>\" <<EMAIL>>"
        //  "To: admin <<EMAIL>>"
        //  "Subject: EULA1"
        //  "X-Priority: 3"
        //  "X-GUID: B6CC77AE-D593-4C20-8043-6977586229A9"
        //  "X-Has-Attach: yes"
        //  "X-Mailer: Foxmail **********[cn]"
        //  "Mime-Version: 1.0"
        // },
                {"GET /hello HTTP/1.1\r\n"
         "Host: p3p.sogou.com\r\n"
         "Cache-Control: no-cache\r\n"
         "User-Agent: firefox\r\n"
         "Cookie: name=John\r\n"
         "\r\n"},
    };

    this->testDissect(httpStream);

    ASSERT_EQ(msgArray_.size(), 2);

    // check msg1
    EXPECT_PRECORD_MATCH(EQ,    msgArray_[0], "Msg-Number", uinteger, 0);
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Method",     string, "GET");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "URI",        string, "/seupdater.gif?res=0");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Version",    string, "HTTP/1.1");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Host",       string, "p3p.sogou.com");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "User-Agent", string, "MicroMessenger Client");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[0], "Cookie",     string, "IMEVER=9.8.0.3746");

    // check msg2
    EXPECT_PRECORD_MATCH(EQ,    msgArray_[1], "Msg-Number", uinteger, 1);
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[1], "Method",     string, "GET");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[1], "URI",        string, "/hello");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[1], "Version",    string, "HTTP/1.1");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[1], "Host",       string, "p3p.sogou.com");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[1], "User-Agent", string, "firefox");
    EXPECT_PRECORD_MATCH(STREQ, msgArray_[1], "Cookie",     string, "name=John");
}

