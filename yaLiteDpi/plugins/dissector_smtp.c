#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_parser.h"
#include "yaEngineNext/nxt_util.h"
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/macro.h>

#include <string.h>
#define PROTO_NAME "smtp"

int nxt_parser_smtp_init(nxt_parser_t *parser);
int nxt_parser_smtp_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata);

typedef enum smtp_parsing_state
{
    SMTP_PARSING_ERROR = 0,
    SMTP_PARSING_NEW_MSG,
    SMTP_PARSING_CMD,
    SMTP_PARSING_DATA,
    SMTP_PARSING_HEADER,
    SMTP_PARSING_BODY,
    SMTP_PARSING_COMPLETE,
}smtp_parsing_state;

typedef struct smtp_session_userdata
{
    nxt_direction_enum       dir;
    nxt_parser_t *parser;
    nxt_ringbuf_t *rbuf;
    precord_t *precord;
    smtp_parsing_state pstate;
    uint32_t msgNumber;
    uint32_t bodyRemain;
} smtp_session_userdata;


static
void smtp_userdata_init(nxt_session_t *session _U_, void *userdata)
{
    smtp_session_userdata *u = (smtp_session_userdata *)userdata;
    ya_allocator_t *alloc = nxt_session_get_allocator(NULL, session);

    u->parser = nxt_parser_create_wa(alloc, nxt_parser_smtp_init);
    u->rbuf = nxt_ringbuf_create_wa(alloc, 4000);
    u->precord = NULL;
    u->pstate = SMTP_PARSING_NEW_MSG;
    u->bodyRemain = 0;
}

static
void smtp_userdata_finish(nxt_session_t *session, void *userdata)
{
    smtp_session_userdata *u = (smtp_session_userdata *)userdata;
    if (u->precord != NULL) {
        nxt_session_destroy_record(NULL, session, u->precord);
    }
    ya_allocator_t *alloc = nxt_session_get_allocator(NULL, session);
    nxt_parser_destroy_wa(alloc, u->parser);
    nxt_ringbuf_destroy_wa(alloc, u->rbuf);
}

static
void smtp_on_parsing_msg_complete(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf, smtp_session_userdata *userdata)
{
    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, userdata->precord);
    nxt_session_destroy_record(engine, session, userdata->precord);
    userdata->precord = NULL;
    nxt_parser_reset(userdata->parser);
}

static
int smtp_dissect_message_in_ringbuf(nxt_engine_t *engine, nxt_session_t *session,
                                   nxt_direction_enum dir _U_, nxt_mbuf_t *mbuf _U_, smtp_session_userdata *userdata)
{
    int totalLen = nxt_ringbuf_get_data_length(userdata->rbuf);
    int consumeLen = 0;

    while (consumeLen < totalLen) {
        switch (userdata->pstate) {
        case SMTP_PARSING_ERROR:
        {
            break;
        }
        case SMTP_PARSING_NEW_MSG:
        {
            userdata->precord = nxt_session_create_record(engine, session);
            precord_layer_put_new_layer(userdata->precord, PROTO_NAME);
            precord_put(userdata->precord, "Msg-Number", uinteger, userdata->msgNumber);

            userdata->pstate = SMTP_PARSING_CMD;
            break;
        }
        case SMTP_PARSING_CMD:
        {
            int processLen = nxt_parser_smtp_parse(userdata->parser, nxt_ringbuf_get_data(userdata->rbuf), nxt_ringbuf_get_data_length(userdata->rbuf), userdata->precord);
            uint8_t *data = nxt_ringbuf_get_data(userdata->rbuf);
            if (processLen > 0) {
                nxt_ringbuf_pop_front(userdata->rbuf, processLen);
            }

            // if (precord_fvalue_get(userdata->precord, "data") != NULL && dir == NXT_DIR_S2C) {
            //     userdata->pstate = SMTP_PARSING_HEADER;
            // }

            if (processLen > 3 && data[0] == '3' && data[1] == '5' && data[2] == '4') {
                userdata->pstate = SMTP_PARSING_HEADER;
            }


            // pfield_t *field = precord_field_get_by_name(userdata->precord, "reply_line");
            // if (field != NULL) {
            //     ya_fvalue_t *value = precord_field_get_fvalue(field);
            //     if (ya_fvalue_get_string(value) != NULL) {
            //         printf("reply_line: %s\n", ya_fvalue_get_string(value));
            //     }
            // }
            consumeLen += processLen;
            break;
        }
        case SMTP_PARSING_HEADER:
        {
            int processLen = nxt_parser_smtp_parse(userdata->parser, nxt_ringbuf_get_data(userdata->rbuf), nxt_ringbuf_get_data_length(userdata->rbuf), userdata->precord);
            if (processLen > 0) {
                nxt_ringbuf_pop_front(userdata->rbuf, processLen);
            }
            consumeLen += processLen;

            if (nxt_parser_get_status(userdata->parser) == NXT_PSTATUS_COMPLETE
                && userdata->precord)
            {
                userdata->pstate = SMTP_PARSING_BODY;
            }
            break;
        }
        case SMTP_PARSING_BODY:
        {

            uint32_t gotLen = nxt_ringbuf_get_data_length(userdata->rbuf);
            uint8_t *data = nxt_ringbuf_get_data(userdata->rbuf);

            // printf("got")
            // end flag 2e0d0a
            if (gotLen >= 3 && data[gotLen - 3] == 0x2e && data[gotLen - 2] == 0x0d && data[gotLen - 1] == 0x0a) {
                smtp_on_parsing_msg_complete(engine, session, mbuf, userdata);
                userdata->pstate = SMTP_PARSING_COMPLETE;
            }
            nxt_ringbuf_pop_front(userdata->rbuf, gotLen);

            consumeLen += gotLen;


            break;
        }
        case SMTP_PARSING_COMPLETE:
        {
            uint32_t gotLen = nxt_ringbuf_get_data_length(userdata->rbuf);
            uint8_t *data = nxt_ringbuf_get_data(userdata->rbuf);


            // check mail from  to new_msg
            if (gotLen > 9 && strncmp((char *)data, "MAIL FROM:", 10) == 0) {
                // do not custom rbuf, just compare with "MAIL FROM:"
                userdata->pstate = SMTP_PARSING_NEW_MSG;
                break;
            }

            if (gotLen > 0) {
                nxt_ringbuf_pop_front(userdata->rbuf, gotLen);
            }
            consumeLen += gotLen;
            break;
        }
        default:
        break;
        }
        // nxt_util_show_precord(userdata->precord);
    }
goto OUT;
OUT:
    return consumeLen;
}

static int smtp_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf _U_)
{
    int consumeLen = 0;
    smtp_session_userdata *data = (smtp_session_userdata *)nxt_session_get_userdata(engine, session);
    nxt_direction_enum dir = nxt_engine_regzone_get_direction(engine);

    while (nxt_session_stream_rbread(engine, session, dir, data->rbuf, 0, NULL) > 0) {
        int processLen = smtp_dissect_message_in_ringbuf(engine, session, dir, mbuf, data);
        if (processLen < 0) {
            break;
        }
        consumeLen += processLen;
    }

    return consumeLen;
}

static int smtp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto_ex(db, PROTO_NAME, "smtp");
    pschema_register_field(pschema, "Msg-Number",        YA_FT_UINT32,  "");
    pschema_register_field(pschema, "ehlo",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "helo",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "mail_from",                YA_FT_STRING, "");
    pschema_register_field(pschema, "rcpt_to",                  YA_FT_STRING, "");
    pschema_register_field(pschema, "data",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "quit",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "noop",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "rset",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "vrfy",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "expn",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "help",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "starttls",                 YA_FT_STRING, "");
    pschema_register_field(pschema, "reply_line",               YA_FT_STRING, "");

    pschema_register_field(pschema, "from",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "to",                       YA_FT_STRING, "");
    pschema_register_field(pschema, "subject",                  YA_FT_STRING, "");
    pschema_register_field(pschema, "date",                     YA_FT_STRING, "");
    pschema_register_field(pschema, "message_id",               YA_FT_STRING, "");
    pschema_register_field(pschema, "content_type",             YA_FT_STRING, "");
    pschema_register_field(pschema, "content_transfer_encoding",YA_FT_STRING, "");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "smtp",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = smtp_schema_reg,
    .dissectFun   = smtp_dissect,
    .userdata     = {sizeof(smtp_session_userdata), smtp_userdata_init, smtp_userdata_finish},
    .mountAt      = {
        NXT_MNT_PORT_PAYLOAD("tcp", 587, NULL),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(smtp)
{
    nxt_dissector_register(&gDissectorDef);
}