#!/usr/bin/env python3
"""
ASN.1 Inspector Tool
用于检查ASN.1定义文件并预测asn1c生成的结构名称
"""

import os
import re
import sys
import argparse

class ASN1Inspector:
    def __init__(self):
        self.types = []
        self.choices = []
        self.sequences = []

    def parse_asn1_file(self, asn1_file):
        """解析ASN.1文件并提取类型定义"""
        with open(asn1_file, 'r') as f:
            content = f.read()

        # 移除注释
        content = re.sub(r'--.*?$', '', content, flags=re.MULTILINE)

        # 匹配类型定义
        type_pattern = r'([A-Z][a-zA-Z0-9-]*)\s*::=\s*(SEQUENCE|CHOICE|INTEGER|OCTET\s+STRING|OBJECT\s+IDENTIFIER|BIT\s+STRING|\[APPLICATION\s+\d+\].*?|\[\d+\]\s+IMPLICIT\s+SEQUENCE.*?)'
        matches = re.findall(type_pattern, content, re.MULTILINE | re.DOTALL)

        for match in matches:
            original_name = match[0]
            type_def = match[1].strip()

            # asn1c保持原始名称，不转换连字符
            # 只有在C结构体名称中才转换为下划线
            c_struct_name = original_name.replace('-', '_')

            self.types.append({
                'original_name': original_name,
                'c_name': c_struct_name,
                'header_name': original_name,  # 头文件名保持原始名称
                'definition': type_def
            })

            # 分类类型
            if 'CHOICE' in type_def:
                self.choices.append(c_struct_name)
            elif 'SEQUENCE' in type_def:
                self.sequences.append(c_struct_name)

    def generate_structure_info(self):
        """生成结构信息"""
        info = []
        info.append("=" * 60)
        info.append("ASN.1 结构预测报告")
        info.append("=" * 60)
        info.append("")

        if not self.types:
            info.append("未找到ASN.1类型定义")
            return "\n".join(info)

        info.append(f"发现 {len(self.types)} 个类型定义:")
        info.append("")

        for i, type_info in enumerate(self.types, 1):
            info.append(f"{i}. {type_info['original_name']}")
            info.append(f"   C结构体名称: {type_info['c_name']}_t")
            info.append(f"   类型描述符: asn_DEF_{type_info['c_name']}")

            if type_info['c_name'] in self.choices:
                info.append(f"   枚举类型: {type_info['c_name']}_PR")
                info.append(f"   类型: CHOICE")
            elif type_info['c_name'] in self.sequences:
                info.append(f"   类型: SEQUENCE")
            else:
                info.append(f"   类型: {type_info['definition'][:50]}...")

            info.append("")

        return "\n".join(info)

    def generate_usage_examples(self):
        """生成使用示例"""
        if not self.types:
            return ""

        # 选择第一个类型作为主要示例
        main_type = self.types[0]['c_name']

        examples = []
        examples.append("=" * 60)
        examples.append("使用示例")
        examples.append("=" * 60)
        examples.append("")

        examples.append("1. 包含头文件:")
        examples.append(f'   #include "{main_type}.h"')
        examples.append(f'   #include "ber_decoder.h"')
        examples.append("")

        examples.append("2. 解码ASN.1数据:")
        examples.append(f"   {main_type}_t *message = NULL;")
        examples.append(f"   asn_dec_rval_t result = ber_decode(0, &asn_DEF_{main_type},")
        examples.append(f"                                       (void **)&message, data, data_len);")
        examples.append("")
        examples.append("   if (result.code != RC_OK) {")
        examples.append("       // 解码失败")
        examples.append("       return error;")
        examples.append("   }")
        examples.append("")

        examples.append("3. 访问数据:")
        if main_type in self.choices:
            examples.append("   switch (message->present) {")
            examples.append(f"       case {main_type}_PR_choice1:")
            examples.append("           // 处理choice1")
            examples.append("           break;")
            examples.append(f"       case {main_type}_PR_choice2:")
            examples.append("           // 处理choice2")
            examples.append("           break;")
            examples.append("   }")
        else:
            examples.append("   // 访问字段")
            examples.append("   // message->field_name")
        examples.append("")

        examples.append("4. 释放内存:")
        examples.append(f"   ASN_STRUCT_FREE(asn_DEF_{main_type}, message);")
        examples.append("")

        examples.append("5. 调试打印:")
        examples.append(f"   asn_fprint(stdout, &asn_DEF_{main_type}, message);")
        examples.append("")

        return "\n".join(examples)

    def generate_discovery_header(self, protocol_name):
        """生成发现头文件内容"""
        if not self.types:
            return ""

        header = []
        header.append(f"/*")
        header.append(f" * Auto-generated ASN.1 structure discovery header for {protocol_name.upper()}")
        header.append(f" * This file helps developers identify available ASN.1 structures")
        header.append(f" */")
        header.append("")
        header.append(f"#ifndef _ASN1_DISCOVERY_{protocol_name.upper()}_H_")
        header.append(f"#define _ASN1_DISCOVERY_{protocol_name.upper()}_H_")
        header.append("")
        header.append("/* Include all generated ASN.1 headers */")

        for type_info in self.types:
            header.append(f'#include "{type_info["header_name"]}.h"')

        header.append("")
        header.append(f"/* ASN.1 Type Definitions for {protocol_name.upper()} */")

        for type_info in self.types:
            header.append("")
            header.append(f"/* Type: {type_info['c_name']} */")
            header.append(f"/* Structure: {type_info['c_name']}_t */")
            header.append(f"/* Descriptor: asn_DEF_{type_info['c_name']} */")
            if type_info['c_name'] in self.choices:
                header.append(f"/* Enum: {type_info['c_name']}_PR */")

        # 添加使用示例
        main_type = self.types[0]['c_name']
        header.append("")
        header.append("/* Usage Examples:")
        header.append(" *")
        header.append(" * 1. Decode a message:")
        header.append(f" *    {main_type}_t *msg = NULL;")
        header.append(f" *    asn_dec_rval_t result = ber_decode(0, &asn_DEF_{main_type},")
        header.append(f" *                                       (void **)&msg, data, data_len);")
        header.append(" *")
        header.append(" * 2. Free decoded structure:")
        header.append(f" *    ASN_STRUCT_FREE(asn_DEF_{main_type}, msg);")
        header.append(" *")
        header.append(" * 3. Print structure (debug):")
        header.append(f" *    asn_fprint(stdout, &asn_DEF_{main_type}, msg);")
        header.append(" */")
        header.append("")
        header.append(f"#endif /* _ASN1_DISCOVERY_{protocol_name.upper()}_H_ */")

        return "\n".join(header)

def main():
    parser = argparse.ArgumentParser(description='ASN.1 Inspector - 检查ASN.1文件并预测生成的C结构')
    parser.add_argument('protocol_name', nargs='?', help='协议名称（用于头文件生成）')
    parser.add_argument('asn1_file', nargs='?', help='ASN.1定义文件路径')
    parser.add_argument('additional_files', nargs='*', help='额外的ASN.1文件')
    parser.add_argument('-o', '--output', help='输出文件路径（可选）')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--header', action='store_true', help='生成C头文件格式')

    args = parser.parse_args()

    # 支持两种调用方式：
    # 1. python script.py file.asn (原始方式)
    # 2. python script.py protocol_name output_file file1.asn [file2.asn] (CMake方式)

    if args.header or (args.protocol_name and args.asn1_file and args.additional_files):
        # CMake调用方式: protocol_name output_file asn1_file1 [asn1_file2...]
        protocol_name = args.protocol_name
        output_file = args.asn1_file  # 第二个参数是输出文件
        asn1_files = args.additional_files  # 剩余参数是ASN.1文件

        inspector = ASN1Inspector()

        for asn1_file in asn1_files:
            if not os.path.exists(asn1_file):
                print(f"错误: 文件不存在: {asn1_file}")
                sys.exit(1)
            try:
                inspector.parse_asn1_file(asn1_file)
            except Exception as e:
                print(f"错误: 解析ASN.1文件失败: {e}")
                sys.exit(1)

        # 生成头文件
        header_content = inspector.generate_discovery_header(protocol_name)

        with open(output_file, 'w') as f:
            f.write(header_content)
        print(f"Generated discovery header: {output_file}")

    else:
        # 原始调用方式
        asn1_file = args.protocol_name or args.asn1_file
        if not asn1_file or not os.path.exists(asn1_file):
            print(f"错误: 文件不存在: {asn1_file}")
            sys.exit(1)

        inspector = ASN1Inspector()

        try:
            inspector.parse_asn1_file(asn1_file)
        except Exception as e:
            print(f"错误: 解析ASN.1文件失败: {e}")
            sys.exit(1)

        # 生成报告
        report = []
        report.append(f"ASN.1文件: {asn1_file}")
        report.append("")
        report.append(inspector.generate_structure_info())
        report.append(inspector.generate_usage_examples())

        output_text = "\n".join(report)

        if args.output:
            with open(args.output, 'w') as f:
                f.write(output_text)
            print(f"报告已保存到: {args.output}")
        else:
            print(output_text)

if __name__ == '__main__':
    main()
