#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "gtp_u"

static
int gtp_u_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);


    uint16_t type = nxt_mbuf_get_uint8(mbuf, 1);
    printf("gtp_u type = %d\n", type);

    nxt_handoff_set_key_of_number(engine, type);
    return 8;
}

static
int gtp_u_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "gtp_u");
    pschema_register_field(pschema,    "gtp_u",            YA_FT_UINT16,  "gtp_u");
     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "gtp_u",
    .schemaRegFun = gtp_u_schema_reg,
    .dissectFun   = gtp_u_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 2152),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(gtp_u)
{
    nxt_dissector_register(&gDissectorDef);
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "gtp_u", 0xff, "ipv4");
}
