#include <stdlib.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>

// BCD(十进制大端)转十进制数值(存在F分割)
static size_t JSC_BCD_DEC(const uint8_t *data, int datalen) {
  unsigned long long num = 0;
  unsigned char      a, b;
  for (int i = 0; i < datalen; i++) {
    a = 0X0F & data[i];
    b = 0X0F & (data[i] >> 4);
    if (0x0F != a) {
      num = num * 10 + a;
    }
    if (0x0F != b) {
      num = num * 10 + b;
    }
  }
  return num;
}

// BCD(十六进制大端)转十六进制数值(无F分割)
static size_t JSC_BCD_HEX(const uint8_t *data, int datalen) {
  unsigned long long num = 0;
  unsigned char      a, b;
  for (int i = 0; i < datalen; i++) {
    a = 0X0F & data[i];
    b = 0X0F & (data[i] >> 4);
    num = num * 16 + a;
    num = num * 16 + b;
  }
  return num;
}

static unsigned short JSC_PLMN(const uint8_t *data, unsigned short *ecgiMnc) {
  char           buff[64] = {0};
  unsigned short mcc = 0;
  unsigned short mnc = 0;
  unsigned char  h;
  unsigned char  l;

  for (int i = 0; i < 2; i++) {
    h = 0X0F & data[i];
    l = 0X0F & (data[i] >> 4);
    mcc *= 10;
    mcc += ((0xF == h) ? 0 : h);
    mcc *= 10;
    mcc += ((0xF == l) ? 0 : l);
  }
  mcc /= 10;

  {
    l = 0X0F & data[2];
    mnc = (mnc * 10 + (0x0F != l)) ? l : 0;
  }
  if (ecgiMnc) {
    *ecgiMnc = mnc;
  }
  snprintf(buff, sizeof(buff), "%u%02u", mcc, mnc);
  return atoi(buff);  //返回网络字节序(16进制输出格式,统一规范)
}

static int hwzz_ParseTrailer_JSC_tag(precord_t *precord, nxt_mbuf_t *mbuf, int offset, char tag, int datalen) {
  switch ((unsigned char)tag) {
    case 0:
      if (datalen < 64) {
        precord_put(precord, "fixed_account", bytes, nxt_mbuf_get_raw(mbuf, offset), datalen);
      }
      break;
    case 1:  // IMSI  8 字节
      if (8 == datalen) {
        precord_put(precord, "imsi", uinteger64, JSC_BCD_DEC(nxt_mbuf_get_raw(mbuf, offset), datalen));
      }
      break;

    case 2:  // MSISDN  8 字节
      if (8 == datalen) {
        precord_put(precord, "msisdn", uinteger64, JSC_BCD_DEC(nxt_mbuf_get_raw(mbuf, offset), datalen));
      }
      break;

    case 3:  // IMEI  8 字节
      if (8 == datalen) {
        precord_put(precord, "imei", uinteger64, JSC_BCD_DEC(nxt_mbuf_get_raw(mbuf, offset), datalen));
      }
      break;

    case 4:  // MEID  7 字节
      if (7 == datalen) {
        precord_put(precord, "imei", uinteger64, JSC_BCD_HEX(nxt_mbuf_get_raw(mbuf, offset), datalen));
      }
      break;

    case 5:  // ESN  4 字节
      if (4 == datalen) {
        precord_put(precord, "esn", uinteger64, JSC_BCD_HEX(nxt_mbuf_get_raw(mbuf, offset), datalen));
      }
      break;

    case 6:  // APN  N 字节
      if (datalen <= 16) {
        precord_put(precord, "fixed_account", bytes, nxt_mbuf_get_raw(mbuf, offset), datalen);
      }
      break;

    case 7:  // CGI = MCC&MNC(3) + LAC(2) + CellID(2)
      if (7 == datalen) {
        precord_put(precord, "plmn_id", uinteger, JSC_PLMN(nxt_mbuf_get_raw(mbuf, offset), NULL));
        precord_put(precord, "lac", uinteger, nxt_mbuf_get_uint16(mbuf, offset + 3));
        precord_put(precord, "ci", uinteger, nxt_mbuf_get_uint16(mbuf, offset + 3 + 2));
      }
      break;

    case 8:  // SAI = MCC&MNC(3) + LAC(2) + SAC(2)
      if (7 == datalen) {
        precord_put(precord, "plmn_id", uinteger, JSC_PLMN(nxt_mbuf_get_raw(mbuf, offset), NULL));
        precord_put(precord, "lac", uinteger, nxt_mbuf_get_uint16(mbuf, offset + 3));
        precord_put(precord, "sac", uinteger, nxt_mbuf_get_uint16(mbuf, offset + 3 + 2));
      }
      break;

    case 9:  // RAI = MCC&MNC(3) + LAC(2) + RAC(2)
      if (7 == datalen) {
        precord_put(precord, "plmn_id", uinteger, JSC_PLMN(nxt_mbuf_get_raw(mbuf, offset), NULL));
        precord_put(precord, "lac", uinteger, nxt_mbuf_get_uint16(mbuf, offset + 3));
      }
      break;

    case 10:  // TAI = MCC&MNC(3) + TAC(2)
      if (5 == datalen) {
        precord_put(precord, "plmn_id", uinteger, JSC_PLMN(nxt_mbuf_get_raw(mbuf, offset), NULL));
        precord_put(precord, "tac",       uinteger, nxt_mbuf_get_uint16_ntoh(mbuf, offset));
      }
      break;

    case 11:  // ECGI = = MCC&MNC(3) + ECI(4)
      if (7 == datalen) {
        unsigned short ecgiMnc = 0;
        unsigned short plmnId = JSC_PLMN(nxt_mbuf_get_raw(mbuf, offset), (unsigned short *)&ecgiMnc);
        precord_put(precord, "plmn_id", uinteger, plmnId);
        precord_put(precord, "ecgi_mnc", uinteger, ecgiMnc);
        uint32_t value = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 3);
        value = value & 0x0FFFFFFF;
        precord_put(precord, "uli", uinteger, value);
        precord_put(precord, "ecgi", uinteger, value);
      }
      break;
    case 12:  // BSID
      if (6 == datalen) {
        int bsid = JSC_BCD_HEX(nxt_mbuf_get_raw(mbuf, offset), datalen);
        precord_put(precord, "bsid", bytes, (const uint8_t *)&bsid, sizeof(bsid));
      }
      break;
    case 13:
      precord_put(precord, "ruleid", uinteger, nxt_mbuf_get_uint8(mbuf, offset));
      break;
    case 14:
      {
        uint8_t direction = nxt_mbuf_get_uint8(mbuf, offset);
        precord_put(precord, "bsid", boolean, direction);
      }
      break;
    case 15:
      if (4 == datalen) {
        precord_put(precord, "ruleid", bytes, (const unsigned char *)nxt_mbuf_get_raw(mbuf, offset), datalen);
      }
      break;
    case 16:
      if (datalen < 128) {
        precord_put(precord, "label", bytes, (const unsigned char *)nxt_mbuf_get_raw(mbuf, offset), datalen);
      }
      break;
    default:
      break;
  }
  return 0;
}

static int hwzz_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf) {
  precord_t *precord = nxt_engine_pktzone_get_precord(engine);
  precord_layer_move_cursor(precord, "4G");
  int offset = 0;
  int trailerLen = nxt_mbuf_get_length(mbuf);

  if (trailerLen < 4) {
    /* printf("bad dns packet, too short:%d\n", nxt_mbuf_get_length(mbuf)); */
    return NXT_DISSECT_ST_VERIFY_FAILED;
  }

  const uint8_t *data = nxt_mbuf_get_raw(mbuf, offset);
  if ((uint8_t)data[0] != 0xff && (uint8_t)data[1] != 0xfe) {
    return 0;
  }
  offset += 2;
  /*trailerLen 长度不包括前三个字节(2个字节标签特征码+1个字节长度指示) */
  while (offset < trailerLen) {
    uint8_t tag = nxt_mbuf_get_uint8(mbuf, offset);
    offset += 1;
    uint8_t len = nxt_mbuf_get_uint8(mbuf, offset);
    offset += 1;
    hwzz_ParseTrailer_JSC_tag(precord, mbuf, offset, tag, len);
    offset += len;
  }
  return offset;
}

static int hwzz_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db _U_) {
  pschema_t *schema4G = pschema_register_base(db, "4G", "4G base layer", "#NONE");

  pschema_register_field(schema4G, "fixed_account", YA_FT_BYTES, "fixed_account");
  pschema_register_field(schema4G, "esn", YA_FT_UINT64, "esn");
  pschema_register_field(schema4G, "apn", YA_FT_UINT64, "apn");
  pschema_register_field_ex(schema4G, "plmn_id", YA_FT_UINT16, "plmn_id", YA_DISPLAY_BASE_HEX);
  pschema_register_field_ex(schema4G, "ecgi_mnc", YA_FT_UINT16, "ecgi_mnc", YA_DISPLAY_BASE_HEX);
  pschema_register_field(schema4G, "lac", YA_FT_UINT64, "lac");
  pschema_register_field(schema4G, "ci", YA_FT_UINT64, "ci");
  pschema_register_field(schema4G, "sac", YA_FT_UINT64, "sac");
  pschema_register_field_ex(schema4G, "uli", YA_FT_UINT32, "uli", YA_DISPLAY_BASE_HEX);
  pschema_register_field(schema4G, "ecgi", YA_FT_UINT64, "ecgi");
  pschema_register_field(schema4G, "bsid", YA_FT_UINT64, "bsid");
  pschema_register_field(schema4G, "direction", YA_FT_UINT64, "direction");
  pschema_register_field(schema4G, "ruleid", YA_FT_UINT64, "ruleid");
  pschema_register_field(schema4G, "label", YA_FT_UINT64, "label");
  pschema_register_field(schema4G, "bs", YA_FT_UINT64, "bs");
  pschema_register_field(schema4G, "bflag", YA_FT_UINT64, "bflag");
  pschema_register_field(schema4G, "operator", YA_FT_STRING, "operator");
  pschema_register_field(schema4G, "rat", YA_FT_UINT64, "rat");
  pschema_register_field_ex(schema4G, "teid", YA_FT_UINT32, "teid", YA_DISPLAY_BASE_HEX);
  pschema_register_field(schema4G, "msisdn", YA_FT_UINT64, "msisdn");
  pschema_register_field(schema4G, "imei", YA_FT_UINT64, "imei");
  pschema_register_field(schema4G, "imsi", YA_FT_UINT64, "imsi");
  pschema_register_field_ex(schema4G, "tac", YA_FT_UINT16, "tac", YA_DISPLAY_BASE_HEX);

  return 0;
}

static nxt_dissector_def_t gDissectorDef = {
    .name = "hwzz",
    .type = NXT_DISSECTOR_TYPE_TRAILER,
    .schemaRegFun = hwzz_schema_reg,
    .dissectFun = hwzz_dissect,
    .mountAt =
        {
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(hwzz) { nxt_dissector_register(&gDissectorDef); }
