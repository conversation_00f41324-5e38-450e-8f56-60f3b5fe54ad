#include <yaEngineNext/nxt_parser_core.h>

%%{
    machine fsm;
    alphtype unsigned char;
    include basic "nxt_parser_basic.rl";

    action done { fbreak; }
    action helo { OUT("helo", parser->s); }
    action ehlo { OUT("ehlo", parser->s); }
    action mail_from { OUT("mail_from", parser->s); }
    action rcpt_to { OUT("rcpt_to", parser->s); }
    action data { OUT("data", parser->s); }

    action reply_line { OUT("reply_line", parser->s); }
    action h_from { OUT("from", parser->s); }
    action h_date { OUT("date", parser->s); }
    action h_to { OUT("to", parser->s); }
    action h_subject { OUT("subject", parser->s); }


    CRLF = ( "\r\n" | "\n" ) ;
    SP = 0x20;
    CR = 0x0D;
    LF = 0x0A;
    HT = 0x09;
    LWS = (CRLF)? (SP | HT){1,};
    SWS = (LWS)?;

    dec_octet = ( digit
                | ("1"-"9") digit
                | "1" digit{2}
                | "2" ("0"-"4") digit
                | "25" ("0"-"5")
                ) ;

    IPv4address = dec_octet "." dec_octet "." dec_octet "." dec_octet;
    h16           = ( xdigit{1,4} ) ;
    ls32          = ( ( h16 ":" h16 ) | IPv4address ) ;
    IPv6address   = (                               6( h16 ":" ) ls32
                    |                          "::" 5( h16 ":" ) ls32
                    | (                 h16 )? "::" 4( h16 ":" ) ls32
                    | ( ( h16 ":" ){1,} h16 )? "::" 3( h16 ":" ) ls32
                    | ( ( h16 ":" ){2,} h16 )? "::" 2( h16 ":" ) ls32
                    | ( ( h16 ":" ){3,} h16 )? "::"    h16 ":"   ls32
                    | ( ( h16 ":" ){4,} h16 )? "::"              ls32
                    | ( ( h16 ":" ){5,} h16 )? "::"              h16
                    | ( ( h16 ":" ){6,} h16 )? "::"
                    ) ;

    Let_dig = (alpha | digit);
    Ldh_str = ( Let_dig | "-" )*;
    sub_domain = Let_dig [Ldh_str];
    FQDN = sub_domain ( "." sub_domain )*;
    DOMAIN = FQDN;
    address_literal = "[" ( IPv4address | IPv6address | FQDN ) "]";
                          # 20-30 -      0  30
    #textstring = (HT | SP | [\x20-\x7E])+;
    # textstring = (HT | SP | [\x20-\x7E] | "-")+;
    textstring = (0x20..0x7E)+;
    Reply_code = ([2-5] [0-5] [0-9]);
    # Reply_line = (Reply_code "-" textstring >mark %reply_line  | Reply_code (SP textstring) >mark %reply_line ) CRLF;
    Reply_line = (Reply_code "-" textstring CRLF) *  Reply_code (SP textstring) >mark %reply_line  CRLF;
    HELO = ("HELO" SP DOMAIN) >mark %helo;
    EHLO = ("EHLO" SP textstring) >mark %ehlo;
    MAIL = "MAIL FROM:" SP textstring >mark %mail_from;
    RCPT = "RCPT TO:" SP textstring >mark %rcpt_to;
    DATA = ("DATA") >mark %data;
    unknown_cmd = (any - CRLF)+;

    # message header field   rfc 822
    LWSP = SP | HT | LF;
    # field-name  =  1*<any CHAR, excluding CTLs, SPACE, and ":">
    # field-body  =  field-body-contents [CRLF LWSP-char field-body]
    # field       =  field-name ":" [ field-body ] CRLF
    field_name = (any - CRLF - SP - ":")+;
    field_content = (any - CRLF)+;

    date = "Date:" SP+ field_content >mark %h_date ;
    From = "From:" SP+ field_content >mark %h_from ;
    To = "To:" SP+ field_content >mark %h_to ;
    Subject = "Subject:" SP+ field_content >mark %h_subject ;

    field_value = field_content (CRLF LWSP+ field_content)*;
    unknown_field = field_name ":" field_value ;

    header_field = (date | From | To | Subject | unknown_field) :> CRLF;

    Request = (HELO | EHLO | MAIL | RCPT | DATA) :> CRLF;
    Response = (Reply_line | unknown_cmd) :> CRLF;


    Message = (Request | Response | header_field)* (CRLF);
    main := Message @done;
}%%

%% write data;


int nxt_parser_smtp_init(nxt_parser_t *parser)
{
    int cs = 0;
    %% write init;
    parser->ragelCS = cs;
    return 0;
}


int nxt_parser_smtp_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata)
{
    int cs = parser->ragelCS;
    const uint8_t *p = buff;
    const uint8_t *pe = buff + len;
    const uint8_t *eof = pe;

    %% write exec;

    parser->ragelPoint = p;
    parser->ragelCS = cs;

    if (cs == fsm_error)
    {
        parser->status = NXT_PSTATUS_ERROR;
        return -1;
    }

    if (cs >= fsm_first_final)
    {
        parser->status = NXT_PSTATUS_COMPLETE;
    }
    else
    {
        parser->status = NXT_PSTATUS_PARTIAL;
    }

    return parser->ragelPoint - buff;
}